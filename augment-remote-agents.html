<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产就绪的AI：远程代理现已在VS Code中全面可用</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --google-blue: #4285F4;
            --google-red: #DB4437;
            --google-yellow: #F4B400;
            --google-green: #0F9D58;
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --bg-light: #f8f9fa;
            --bg-dark: #202124;
            --border-color: #dadce0;
            --highlight-bg: #e8f0fe;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-light);
            padding: 0;
            margin: 0;
            font-size: 18px;
            font-weight: 400;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            overflow: hidden;
        }

        header {
            background-color: var(--google-blue);
            color: white;
            padding: 60px 40px;
            text-align: center;
        }

        h1 {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 20px;
            line-height: 1.2;
            letter-spacing: -1px;
        }

        h2 {
            font-size: 2.8rem;
            font-weight: 700;
            margin: 60px 0 30px;
            color: var(--google-blue);
            letter-spacing: -0.5px;
            line-height: 1.2;
        }

        h3 {
            font-size: 2rem;
            font-weight: 700;
            margin: 40px 0 20px;
            color: var(--text-primary);
        }

        h4 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 30px 0 15px;
            color: var(--google-red);
        }

        .author {
            font-size: 1.5rem;
            font-weight: 500;
            margin-top: 10px;
        }

        .date {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-top: 10px;
        }

        .content {
            padding: 40px;
            background: white;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.2rem;
            line-height: 1.7;
        }

        .highlight {
            font-weight: 700;
            color: var(--google-blue);
        }

        .stat-number {
            font-size: 1.4rem;
            font-weight: 900;
            color: var(--google-red);
        }

        .key-point {
            background-color: var(--highlight-bg);
            border-left: 5px solid var(--google-blue);
            padding: 25px;
            margin: 30px 0;
            border-radius: 4px;
        }

        .key-point p {
            margin-bottom: 0;
            font-weight: 500;
        }

        .section {
            margin-bottom: 60px;
        }

        ul, ol {
            margin: 20px 0 30px 20px;
        }

        li {
            margin-bottom: 15px;
            font-size: 1.2rem;
            line-height: 1.6;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: var(--google-blue);
            color: white;
            padding: 20px;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .card-body {
            padding: 25px;
        }

        .card-body p {
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .card-body ul {
            margin-left: 20px;
        }

        .card-body li {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .footer {
            background-color: var(--bg-dark);
            color: white;
            padding: 40px;
            text-align: center;
            font-size: 1rem;
        }

        .quote {
            font-size: 2rem;
            font-weight: 300;
            font-style: italic;
            color: var(--google-blue);
            text-align: center;
            max-width: 800px;
            margin: 60px auto;
            line-height: 1.4;
        }

        .divider {
            height: 4px;
            background: var(--google-blue);
            width: 100px;
            margin: 60px auto;
            border-radius: 2px;
        }

        .emphasis {
            font-weight: 700;
            color: var(--google-red);
            font-size: 1.1em;
        }

        .testimonial {
            background-color: #f8f9fa;
            border-left: 4px solid var(--google-green);
            padding: 20px;
            margin: 30px 0;
            border-radius: 4px;
            font-style: italic;
        }

        .testimonial-author {
            font-weight: 700;
            color: var(--google-green);
            margin-top: 10px;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 3rem;
            }
            h2 {
                font-size: 2.2rem;
            }
            h3 {
                font-size: 1.6rem;
            }
            .content {
                padding: 20px;
            }
            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>生产就绪的AI：远程代理现已在VS Code中全面可用</h1>
            <div class="author">Augment Code 团队</div>
            <div class="date">2025年6月5日</div>
        </div>
    </header>

    <div class="container">
        <div class="content">
            <!-- 引言引用 -->
            <div class="quote">
                "想象一下，拥有一支AI驱动的助手团队，他们可以同时探索多个方向，而您专注于最关键的决策。"
            </div>

            <!-- 关键点 -->
            <div class="key-point">
                <p>今天，我们很高兴宣布 <span class="emphasis">Augment Code远程代理</span> 的全面可用性。远程代理使工程师能够在云端并发运行多个开发任务，扩展您探索、原型设计和迭代的能力，突破线性时间的限制。</p>
            </div>

            <!-- 第一部分 -->
            <div class="section">
                <h2>工程中的时间悖论</h2>
                <p>优秀的工程师总是在与一个简单的事实作斗争：<span class="highlight">你无法做完所有想做的事情</span>。战略举措被推迟，有前景的实验被搁置，创新在维护工作占据中心舞台时等待。</p>
                
                <p>远程代理不仅仅是完成任务——它们扩展了您团队的交付能力。</p>

                <h4>使用远程代理来：</h4>
                <ul>
                    <li><span class="highlight">处理技术债务：</span> 修复那些永远不会进入优先级列表的小错误和痛点</li>
                    <li><span class="highlight">自信地重构：</span> 重构代码的同时保持约定并确保无错误运行</li>
                    <li><span class="highlight">提升测试覆盖率：</span> 为新功能和现有功能自动生成全面的单元测试</li>
                    <li><span class="highlight">探索多种解决方案：</span> 运行并行代理来原型化替代实现</li>
                    <li><span class="highlight">加速文档编写：</span> 为库和功能生成详细文档</li>
                </ul>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 用户评价部分 -->
            <div class="section">
                <h2>用户评价</h2>
                
                <div class="testimonial">
                    <p>"我认为软件生产力的下一个重大飞跃不是来自'氛围编程'。它来自于消除真实代码库中的繁重工作。Augment Code的新远程代理处理不稳定的测试、过时的文档和繁琐的重构——最多可以使用 <span class="stat-number">10个</span> 自主代理。"</p>
                    <div class="testimonial-author">— Eric Schmidt，Google前执行主席兼CEO</div>
                </div>

                <div class="testimonial">
                    <p>"远程代理非常适合具有明确范围的任务和明确定义的结果。学会将开发分解为具有明确范围和明确定义结果的任务将成为一项高杠杆的软件开发技能。"</p>
                    <div class="testimonial-author">— Kent Beck，极限编程发明者</div>
                </div>

                <div class="testimonial">
                    <p>"在使用远程代理一段时间后，我已经可以确定这就是未来。远程代理迫使您作为开发者提升思维水平。现在，如果您能想到清晰的并行功能来开发，您就可以显著提高吞吐量。"</p>
                    <div class="testimonial-author">— Chris Dunlop，Cub Digital CEO兼联合创始人</div>
                </div>
            </div>

            <!-- 差异化特性 -->
            <div class="section">
                <h2>Augment远程代理的独特之处</h2>
                <p>我们是 <span class="highlight">第一个在5月7日宣布远程代理功能</span> 的公司。从那时起，我们看到竞争对手也推出了类似功能——但并非所有AI编码工具都是平等的。</p>

                <!-- 卡片网格 -->
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-red);">专用云工作器</div>
                        <div class="card-body">
                            <p>Augment启动独立的容器化代理，在您注销后继续编码，提供可直接合并的PR。与困在单一环境中的工具不同，我们的代理在整个基础设施中自主工作。</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-green);">智能上下文管理</div>
                        <div class="card-body">
                            <p>我们的语义索引在毫秒内检索相关代码片段，确保代理在复杂的多仓库项目中保持上下文。不再丢失线索或依赖暴力上下文窗口。</p>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-yellow);">企业级隐私</div>
                        <div class="card-body">
                            <p>通过不可提取的架构和严格的无训练保证，Augment消除了在严肃工程团队中减缓AI采用的数据不确定性。</p>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">真正的工作流控制</div>
                        <div class="card-body">
                            <p>细粒度的代理管理让您实时启动、监控和终止任务。</p>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-red);">无缝集成</div>
                        <div class="card-body">
                            <p>直接从您首选的IDE启动代理，不会干扰您现有的工作流程。Augment适应您，而不是相反。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图片部分 -->
            <div style="margin: 40px 0; text-align: center;">
                <figure>
                    <img src="https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/6840d2ef1a8604acec06d173_Blog%20Image%20-%20remote%201%20(1).png" alt="Augment Code远程代理界面" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                    <figcaption style="margin-top: 15px; color: var(--text-secondary); font-size: 1rem;">Augment Code远程代理在VS Code中的工作界面</figcaption>
                </figure>
            </div>

            <!-- 软件开发的未来 -->
            <div class="section">
                <h2>软件开发一直关乎上下文，而非编码</h2>
                <p>软件开发正在转型，AI是催化剂。这不是要取代开发者，而是从根本上改变我们的工作方式。今天的工程师正在成为智能系统的编排者，而不仅仅是编码者。</p>
                
                <p>最有价值的技能不再是编写每一行代码，而是 <span class="highlight">策划、指导和审查AI生成的工作</span>。LLM可以处理曾经消耗我们时间的重复、繁琐任务。它们不会取代我们的判断——它们会放大它。</p>

                <p>这不是威胁；这是机遇。我们正在提高软件开发的底线，使复杂任务更容易获得，同时释放有才华的工程师去解决更具挑战性的问题。</p>
            </div>

            <!-- 开始使用 -->
            <div class="section">
                <h2>开始使用</h2>
                <div class="key-point">
                    <p>远程代理现已在VS Code中全面可用。注册 <span class="stat-number">14天</span> 免费试用，改变您的工程团队工作方式。</p>
                </div>
            </div>

            <!-- 结束引用 -->
            <div class="quote">
                "软件开发的未来不是关于编写更多代码，而是关于更智能地工作。"
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>© 2025 Augment Code | 生产就绪的AI开发工具</p>
        </div>
    </div>
</body>
</html>
